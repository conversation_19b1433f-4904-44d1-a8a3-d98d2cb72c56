<?php
require_once 'PHP/config.php';
require_once 'PHP/language.php';
require_once 'PHP/affiliate_tracker.php';
?>
<!DOCTYPE html>
<html lang="<?= $lang ?>">
<head>
    <title><?= t('site_title') ?></title>
    <meta charset="UTF-8" />
    <meta name="robots" content="noarchive" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="author" content="Kelvin KMS" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
    <link rel="icon" type="image/png" sizes="16x16" href="/Favicon/KMS_Logo_16.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/Favicon/KMS_Logo_32.png" />
    <link rel="icon" type="image/png" sizes="96x96" href="/Favicon/KMS_Logo_96.png" />
    <link rel="icon" type="image/png" sizes="512x512" href="/Favicon/KMS_Logo_512.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/Favicon/KMS_Logo_152.png" />
    <link rel="mask-icon" href="/Favicon/KMS_Logo.svg" color="#5bbad5" />
    <!-- <link rel="manifest" href="/manifest.json" /> -->
    <!-- CSS Files -->
    <link rel="stylesheet" href="CSS/main.css" />
    <link rel="stylesheet" href="CSS/style.css" />
    <link rel="stylesheet" href="CSS/custom-modal.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/material_blue.css" />
    <link rel="stylesheet" href="CSS/live_chat.css" />
    <style>
        input::placeholder { color: white; opacity: 1; }
        .section p {
            margin: 7px !important; /* Shorten paragraph spacing */
        }
        .section h1, .section h2 {
            margin: 7px !important; /* Adjust space between title and paragraph */
        }
        .lang-switcher {
            margin-right: 15px;
            background: none;
            border: 1px solid white;
            color: white;
            padding: 5px 10px;
            cursor: pointer;
            border-radius: 5px;
        }
        .lang-switcher:hover {
            background-color: rgba(255,255,255,0.2);
        }
    </style>
    <style>
        /* PC Order Form Styles */
        #pcOrderForm {
            margin-top: 20px;
            padding: 20px;
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
        }
        #pcOrderForm textarea {
            width: 100%;
            height: 150px;
            padding: 10px;
            background-color: #333;
            color: white;
            border: 1px solid #555;
            border-radius: 5px;
            resize: vertical;
        }
        #pcOrderForm button {
            margin-top: 10px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        #pcOrderForm button:hover {
            background-color: #0056b3;
        }
        .login-prompt {
            margin-top: 20px;
            padding: 20px;
            background-color: rgba(255, 200, 0, 0.2);
            border: 1px solid #ffc800;
            color: #ffc800;
            border-radius: 8px;
            text-align: center;
        }
    </style>
    <style>
        .live-chat-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background-color: #007bff;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 60px;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            z-index: 1000;
        }
        .live-chat-button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <!-- Site Info and Authentication -->
    <div class="site-info">
        <div class="view-counter">
            <div><span id="online-users-container"></span></div>
            <div><?= t('page_views') ?>: <span id="counter">0</span></div>
        </div>
        <div class="auth-buttons">
            <button id="langSwitcher" class="lang-switcher" data-lang="<?= $lang === 'en' ? 'zh-CN' : 'en' ?>">
                <?= $lang === 'en' ? t('lang_zh') : t('lang_en') ?>
            </button>
            <button id="loginBtn"><?= t('login_button') ?></button>
            <button id="registerBtn"><?= t('register_button') ?></button>
        </div>
    </div>

    <!-- Register Modal -->
    <div id="registerModal" class="modal">
        <div class="modal-content register-modal-content">
            <h2><?= t('create_account_title') ?></h2>
            <form id="registerForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="regFirstName"><?= t('first_name') ?> :</label>
                        <input type="text" id="regFirstName" maxlength="30" placeholder="<?= t('fist_name_placeholder') ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="regLastName"><?= t('last_name') ?> :</label>
                        <input type="text" id="regLastName" maxlength="30" placeholder="<?= t('last_name_placeholder') ?>" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="regNickname"><?= t('nickname') ?> :</label>
                    <input type="text" id="regNickname" placeholder="<?= t('nickname_placeholder') ?>" required>
                    <div class="nickname-validation" id="nicknameValidation"><?= t('nickname_hint') ?></div>
                </div>

                <div class="form-group">
                    <label for="regUsername"><?= t('username') ?> :</label>
                    <input type="text" id="regUsername" placeholder="<?= t('username_placeholder') ?>" required>
                    <div class="username-validation" id="usernameHint"><?= t('username_hint_long') ?></div>
                    <div class="username-requirements">
                        <div class="requirement-title"><?= t('username_req_title') ?></div>
                        <div class="requirement" id="uname-length"><?= t('username_req_length') ?></div>
                        <div class="requirement" id="uname-first"><?= t('username_req_first_char') ?></div>
                        <div class="requirement" id="uname-chars"><?= t('username_req_allowed_chars') ?></div>
                        <div class="requirement" id="uname-sensitive"><?= t('username_req_sensitive') ?></div>
                    </div>
                    <div class="username-progress-wrapper">
                        <div class="username-progress" id="usernameProgress"></div>
                    </div>
                    <div class="username-validation" id="usernameValidation"></div>
                </div>

                <div class="form-group">
                    <label><?= t('gender') ?> :</label>
                    <div class="gender-buttons">
                        <button type="button" class="gender-btn" data-gender="male">
                            <span class="gender-icon">♂</span>
                            <span><?= t('gender_male') ?></span>
                        </button>
                        <button type="button" class="gender-btn" data-gender="female">
                            <span class="gender-icon">♀</span>
                            <span><?= t('gender_female') ?></span>
                        </button>
                    </div>
                    <input type="hidden" id="regGender" required>
                </div>

                <div class="form-group">
                    <label><?= t('birthday') ?> :</label>
                    <div class="birthday-picker-container">
                        <div class="birthday-select-group">
                            <label for="regBirthYear"><?= t('year') ?> :</label>
                            <select id="regBirthYear" required></select>
                        </div>
                        <div class="birthday-select-group">
                            <label for="regBirthMonth"><?= t('month') ?> :</label>
                            <select id="regBirthMonth" required></select>
                        </div>
                        <div class="birthday-select-group">
                            <label for="regBirthDay"><?= t('day') ?> :</label>
                            <select id="regBirthDay" required></select>
                        </div>
                    </div>
                    <input type="hidden" id="regBirthday" name="birthday" required>
                    <div class="age-validation" id="ageValidation"></div>
                </div>

                <div class="form-group">
                    <label><?= t('language') ?> :</label>
                    <div class="language-buttons">
                        <button type="button" class="lang-btn" data-lang="en"><?= t('lang_en') ?></button>
                        <button type="button" class="lang-btn" data-lang="zh-CN"><?= t('lang_zh') ?></button>
                        <button type="button" class="lang-btn" data-lang="es"><?= t('lang_es') ?></button>
                        <button type="button" class="lang-btn" data-lang="ja"><?= t('lang_ja') ?></button>
                        <button type="button" class="lang-btn" data-lang="ko"><?= t('lang_ko') ?></button>
                        <button type="button" class="lang-btn" data-lang="other"><?= t('lang_other') ?></button>
                    </div>
                    <input type="hidden" id="regLanguage" required>
                </div>

                <div class="form-group">
                    <label for="regEmail"><?= t('email') ?> :</label>
                    <input type="email" id="regEmail" placeholder="<?= t('email_placeholder') ?>" required>
                    <div class="email-validation" id="emailValidation"></div>
                </div>

                <div class="form-group">
                    <label for="regEmailConfirm"><?= t('email_confirm') ?> :</label>
                    <input type="email" id="regEmailConfirm" placeholder="<?= t('email_confirm_placeholder') ?>" required onpaste="return false">
                    <div class="email-validation" id="emailConfirmValidation"></div>
                </div>

                <div class="form-group">
                    <label for="regPhone"><?= t('phone') ?> :</label>
                    <div class="phone-input-group">
                        <div class="phone-input-container">
                            <span class="phone-prefix">+1</span>
                            <input type="tel" id="regPhone" placeholder="Enter 10-digit phone number" pattern="[0-9]*" inputmode="numeric" maxlength="10" required>
                        </div>
                        <button type="button" id="sendSmsBtn" class="sms-btn"><?= t('send_code') ?></button>
                        <input type="text" id="regSmsCode" placeholder="<?= t('sms_code_placeholder') ?>" maxlength="6" style="display:none; margin-left:10px;">
                        <button type="button" id="verifySmsBtn" class="sms-btn" style="display:none;"><?= t('verify_code') ?></button>
                        <button type="button" id="resetSmsBtn" class="sms-btn" style="display:none;">Reset</button>
                    </div>
                    <div class="phone-format-hint" id="phoneFormatHint">Format: +**************** or +15551234567</div>
                    <div class="phone-validation" id="phoneValidation"></div>
                    <div class="sms-validation" id="smsValidation"></div>
                    <div class="sms-status" id="smsStatus"></div>
                </div>

                <div class="form-group">
                    <label for="regPassword"><?= t('password') ?> :</label>
                    <input type="password" id="regPassword" placeholder="<?= t('password_placeholder') ?>" required>
                    <div class="password-requirements">
                        <div class="requirement-title"><?= t('password_req_title') ?></div>
                        <div class="requirement" id="req-length"><?= t('password_req_length') ?></div>
                        <div class="requirement" id="req-uppercase"><?= t('password_req_uppercase') ?></div>
                        <div class="requirement" id="req-lowercase"><?= t('password_req_lowercase') ?></div>
                        <div class="requirement" id="req-number"><?= t('password_req_number') ?></div>
                        <div class="requirement" id="req-special"><?= t('password_req_special') ?></div>
                    </div>
                    <div class="password-progress-wrapper">
                        <div class="password-progress" id="passwordProgress"></div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="regPasswordConfirm"><?= t('password_confirm') ?> :</label>
                    <input type="password" id="regPasswordConfirm" placeholder="<?= t('password_confirm_placeholder') ?>" required onpaste="return false">
                    <div class="password-match" id="passwordMatch"></div>
                </div>

                <button type="submit" class="register-submit-btn"><?= t('register_button_modal') ?></button>
            </form>
        </div>
    </div>

    <!-- Login Modal -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <h2><?= t('member_login_title') ?></h2>
            <form id="loginForm">
                <input type="text" id="loginUsername" placeholder="<?= t('login_username_placeholder') ?>" required>
                <input type="password" id="loginPassword" placeholder="<?= t('login_password_placeholder') ?>" required>
                <button type="submit"><?= t('login_button_modal') ?></button>
            </form>
        </div>
    </div>

    <!-- Original page content -->
    <div class="container">
        <!-- ... your existing sections from #home to #copyright ... -->
        <div id="home" class="section">
            <h1><?= t('home_welcome') ?></h1>
            <p style="text-align:center; font-size: 20px; color:rgb(255, 255, 255);">E-mail : <EMAIL></p>
            <p><?= t('home_intro1') ?></p>
            <p><?= t('home_intro2') ?></p>
            <p>1 : <?= t('service_vip_pc') ?></p>
            <p>2 : <?= t('service_optimize_media') ?></p>
            <p>3 : <?= t('service_print') ?></p>
            <p>4 : <?= t('service_consult') ?></p>
        </div>
        <div id="portfolio" class="section">
            <h2><?= t('service_vip_pc') ?></h2>
            <p><?= t('vip_pc_desc_1') ?></p>
            <p><?= t('vip_pc_desc_2') ?></p>
            <p><?= t('vip_pc_desc_3') ?></p>

            <?php if (isset($_SESSION['user_id'])): ?>
                <form id="pcOrderForm">
                    <h3><?= t('pc_order_form_title') ?></h3>
                    <textarea id="pcOrderDetails" placeholder="<?= t('pc_order_form_placeholder') ?>" required></textarea>
                    <button type="submit"><?= t('pc_order_form_submit') ?></button>
                </form>
            <?php else: ?>
                <div class="login-prompt">
                    <p><?= t('pc_order_login_prompt') ?></p>
                </div>
            <?php endif; ?>

        </div>
        <div class="slideshow-container">
            <img class="slide" src="/KMS_VIP_PC/VIP_PC_1.jpg" alt="Slide 1">
            <img class="slide" src="/KMS_VIP_PC/VIP_PC_2.jpg" alt="Slide 2">
        </div>
        <div id="optimize" class="section">
            <h2><?= t('service_optimize_media') ?></h2>
            <p><?= t('optimize_desc_1') ?></p>
            <p><?= t('optimize_desc_2') ?></p>
            <p><?= t('optimize_price_photo') ?></p>
            <p><?= t('optimize_price_photo_watermark') ?></p>
            <p><?= t('optimize_price_photo_discount') ?></p>
            <p><?= t('optimize_price_video') ?></p>
            <p><?= t('optimize_price_video_watermark_title') ?></p>
            <p><?= t('optimize_price_video_watermark_easy') ?></p>
            <p><?= t('optimize_price_video_watermark_hard') ?></p>
            <p><?= t('notice_price_change') ?></p>
        </div>
        <div id="print-service" class="section">
            <h2><?= t('service_print') ?></h2>
            <p><?= t('print_desc_1') ?></p>
            <p><?= t('print_desc_2') ?></p>
            <p><?= t('print_desc_3') ?></p>
            <p><?= t('print_paper_1_title') ?></p>
            <p><?= t('print_paper_1_price') ?></p>
            <p><?= t('print_paper_2_title') ?></p>
            <p><?= t('print_paper_2_price') ?></p>
            <p><?= t('print_paper_3_title') ?></p>
            <p><?= t('print_paper_3_price') ?></p>
            <p><?= t('print_paper_4_title') ?></p>
            <p><?= t('print_paper_4_price') ?></p>
            <p><?= t('print_laminating_1_title') ?></p>
            <p><?= t('print_paper_5_title') ?></p>
            <p><?= t('print_paper_5_price') ?></p>
            <p><?= t('print_laminating_2_title') ?></p>
            <p><?= t('print_price_include_fee') ?></p>
            <p><?= t('print_shipping_fee') ?></p>
            <p><?= t('print_discount') ?></p>
            <p><?= t('print_album_title') ?></p>
            <p><?= t('print_album_1') ?></p>
            <p><?= t('print_album_2') ?></p>
            <p><?= t('print_album_3') ?></p>
            <p><?= t('print_album_4') ?></p>
            <p><?= t('notice_price_change') ?></p>
            <p><?= t('print_privacy_title') ?></p>
            <p><?= t('print_privacy_1') ?></p>
            <p><?= t('print_privacy_2') ?></p>
        </div>
        <div id="question-consult" class="section">
            <h2><?= t('service_consult') ?></h2>
            <p><?= t('consult_desc_1') ?></p>
        </div>
        <div id="copyright" class="section">
            <h2><?= t('copyright_title') ?></h2>
            <p><?= t('copyright_year') ?></p>
            <p><?= t('copyright_name') ?></p>
        </div>
    </div>
    <div class="nav">
        <button onclick="scrollToSection('home')"><?= t('nav_home') ?></button>
        <button onclick="scrollToSection('portfolio')"><?= t('service_vip_pc') ?></button>
        <button onclick="scrollToSection('optimize')"><?= t('service_optimize_media') ?></button>
        <button onclick="scrollToSection('print-service')"><?= t('service_print') ?></button>
        <button onclick="scrollToSection('question-consult')"><?= t('service_consult') ?></button>
    </div>
    <div class="mobile-nav">
        <button onclick="scrollToSection('home')">🏠</button>
    </div>

    <!-- Live Chat Button -->
    <div id="liveChatButton" class="live-chat-button">💬</div>

    <!-- Live Chat Widget -->
    <div id="chatWidget" class="chat-widget-container">
        <div class="chat-widget-phone">
            <div class="chat-widget-screen">
                <div class="chat-widget-header">
                    <h3><?= t('live_chat_title') ?></h3>
                    <button id="closeChatWidget" class="chat-widget-close-btn">&times;</button>
                </div>
                <div id="chatInitialForm" class="chat-widget-body">
                    <!-- Guest form appears here -->
                     <p><?= t('live_chat_welcome') ?></p>
                    <form id="guestChatForm">
                        <input type="text" id="chatGuestNickname" placeholder="<?= t('nickname_placeholder') ?>" required>
                        <input type="email" id="chatGuestEmail" placeholder="<?= t('email_placeholder') ?>" required>
                        <button type="submit"><?= t('live_chat_start_button') ?></button>
                    </form>
                </div>
                <div id="chatConversation" class="chat-widget-body" style="display: none;">
                    <!-- Messages will appear here -->
                    <div id="chatMessages" class="chat-messages"></div>
                    <div class="chat-input-area">
                        <form id="chatMessageForm">
                            <input type="text" id="chatMessageInput" placeholder="<?= t('live_chat_input_placeholder') ?>" autocomplete="off" required>
                            <button type="submit">➢</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- JavaScript Files -->
    <script>
        // Pass translations to JavaScript
        const i18n = <?= json_encode($translations) ?>;

        // Language switcher logic
        document.getElementById('langSwitcher').addEventListener('click', function() {
            const newLang = this.dataset.lang;
            // Set cookie to expire in 30 days
            const d = new Date();
            d.setTime(d.getTime() + (30*24*60*60*1000));
            let expires = "expires="+ d.toUTCString();
            document.cookie = "lang=" + newLang + ";" + expires + ";path=/";
            window.location.reload();
        });
    </script>
    <script src="JS/custom-modal.js"></script>
    <script src="JS/main.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="JS/forms.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            
            // --- Initialize Page Functions ---

            // Online Users
            function updateOnlineUsers() {
                fetch('/KelvinKMS.com/PHP/online_users.php')
                    .then(response => {
                        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                        return response.json();
                    })
                    .then(data => {
                        const container = document.getElementById('online-users-container');
                        if (container) {
                            container.innerHTML = data.error ? `<?= t('online_users') ?>Error` : `<?= t('online_users') ?>${data.online_users}`;
                        }
                    })
                    .catch(error => {
                        console.error('Fetch error for online users:', error);
                        const container = document.getElementById('online-users-container');
                        if (container) container.innerHTML = `<?= t('online_users') ?>Error`;
                    });
            }
            updateOnlineUsers();
            setInterval(updateOnlineUsers, 10000);

        });
    </script>
    <script src="JS/orders.js"></script>
    <script src="JS/live_chat.js"></script>
    <script>
        // Separate listener for chat to ensure it runs after all DOM manipulation from other scripts
        document.addEventListener('DOMContentLoaded', function() {
            if(typeof initLiveChat === 'function') {
                initLiveChat();
            }
        });
    </script>
</body>
</html>