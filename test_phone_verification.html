<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phone Verification Test</title>
    <link rel="stylesheet" href="CSS/style.css">
    <style>
        body {
            padding: 20px;
            background-color: #1599c7;
            font-family: sans-serif;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: rgb(5 195 182);
            padding: 30px;
            border-radius: 20px;
            color: white;
        }
        h1 {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Phone Verification Test</h1>
        
        <div class="form-group">
            <label for="regPhone">Phone :</label>
            <div class="phone-input-group">
                <input type="tel" id="regPhone" placeholder="Enter US phone number" pattern="[0-9]*" inputmode="numeric" maxlength="15" required>
                <button type="button" id="sendSmsBtn" class="sms-btn">Send Code</button>
                <input type="text" id="regSmsCode" placeholder="Enter code" maxlength="6" style="display:none; margin-left:10px;">
                <button type="button" id="verifySmsBtn" class="sms-btn" style="display:none;">Verify Code</button>
                <button type="button" id="resetSmsBtn" class="sms-btn" style="display:none;">Reset</button>
            </div>
            <div class="phone-format-hint" id="phoneFormatHint">Format: +**************** or +15552345678</div>
            <div class="phone-validation" id="phoneValidation"></div>
            <div class="sms-validation" id="smsValidation"></div>
            <div class="sms-status" id="smsStatus"></div>
        </div>
    </div>

    <script>
        // Mock translations for testing
        const i18n = {
            js_form_enter_phone_first: "Please enter your phone number first",
            js_form_failed_to_send_code: "Failed to send verification code",
            js_network_error_msg: "Network error. Please check your connection and try again.",
            js_form_code_sent: "Verification code sent successfully!",
            js_form_enter_code: "Please enter the verification code",
            js_form_phone_verified: "Phone number verified successfully!",
            js_form_incorrect_code: "Incorrect verification code"
        };

        // Phone verification functionality
        document.addEventListener('DOMContentLoaded', function() {
            let smsVerified = false;
            
            const regPhone = document.getElementById('regPhone');
            const sendSmsBtn = document.getElementById('sendSmsBtn');
            const regSmsCode = document.getElementById('regSmsCode');
            const verifySmsBtn = document.getElementById('verifySmsBtn');
            const resetSmsBtn = document.getElementById('resetSmsBtn');
            const phoneValidation = document.getElementById('phoneValidation');
            const smsValidation = document.getElementById('smsValidation');

            // Phone validation functions
            function validateUSPhoneNumber(phone) {
                const digits = phone.replace(/\D/g, '');
                
                if (digits.length === 10) {
                    return digits.match(/^[2-9]\d{2}[2-9]\d{2}\d{4}$/);
                } else if (digits.length === 11 && digits.startsWith('1')) {
                    return digits.match(/^1[2-9]\d{2}[2-9]\d{2}\d{4}$/);
                }
                
                return false;
            }
            
            function formatUSPhoneNumber(phone) {
                const digits = phone.replace(/\D/g, '');
                
                if (digits.length === 10) {
                    return `+1${digits}`;
                } else if (digits.length === 11 && digits.startsWith('1')) {
                    return `+${digits}`;
                }
                
                return phone;
            }
            
            // Real-time phone validation
            function updatePhoneValidation() {
                const phone = regPhone.value.trim();
                
                if (!phone) {
                    phoneValidation.innerText = '';
                    phoneValidation.className = 'phone-validation';
                    return;
                }
                
                if (!validateUSPhoneNumber(phone)) {
                    phoneValidation.innerText = 'Please enter a valid US phone number';
                    phoneValidation.className = 'phone-validation invalid';
                } else {
                    phoneValidation.innerText = 'Valid US phone number format';
                    phoneValidation.className = 'phone-validation valid';
                }
            }
            
            // Event listeners
            regPhone.addEventListener('input', updatePhoneValidation);
            regPhone.addEventListener('blur', updatePhoneValidation);
            
            sendSmsBtn.addEventListener('click', () => {
                const phone = regPhone.value.trim();
                if (!phone) {
                    smsValidation.innerText = 'Please enter your phone number first';
                    smsValidation.className = 'sms-validation invalid';
                    return;
                }
                
                if (!validateUSPhoneNumber(phone)) {
                    smsValidation.innerText = 'Please enter a valid US phone number';
                    smsValidation.className = 'sms-validation invalid';
                    return;
                }
                
                const formattedPhone = formatUSPhoneNumber(phone);
                const formDataSms = new FormData();
                formDataSms.append('phone_number', formattedPhone);
                
                smsValidation.innerText = 'Sending verification code...';
                smsValidation.className = 'sms-validation checking';
                sendSmsBtn.disabled = true;
                
                fetch('PHP/send_sms_verification.php', {
                    method: 'POST',
                    credentials: 'same-origin',
                    body: formDataSms
                })
                .then(response => response.json())
                .then(data => {
                    sendSmsBtn.disabled = false;
                    if (data.success) {
                        smsValidation.innerText = 'Verification code sent successfully!';
                        smsValidation.className = 'sms-validation valid';
                        regSmsCode.style.display = 'inline-block';
                        verifySmsBtn.style.display = 'inline-block';
                        resetSmsBtn.style.display = 'inline-block';
                        sendSmsBtn.style.display = 'none';
                        regPhone.disabled = true;
                    } else {
                        smsValidation.innerText = data.message || 'Failed to send verification code';
                        smsValidation.className = 'sms-validation invalid';
                    }
                })
                .catch(() => {
                    sendSmsBtn.disabled = false;
                    smsValidation.innerText = 'Network error. Please check your connection and try again.';
                    smsValidation.className = 'sms-validation invalid';
                });
            });
            
            verifySmsBtn.addEventListener('click', () => {
                const code = regSmsCode.value.trim();
                if (!code) {
                    smsValidation.innerText = 'Please enter the verification code';
                    smsValidation.className = 'sms-validation invalid';
                    return;
                }
                
                const formDataVerify = new FormData();
                formDataVerify.append('phone', formatUSPhoneNumber(regPhone.value.trim()));
                formDataVerify.append('verification_code', code);
                
                smsValidation.innerText = 'Verifying code...';
                smsValidation.className = 'sms-validation checking';
                verifySmsBtn.disabled = true;
                
                fetch('PHP/verify_sms_code.php', {
                    method: 'POST',
                    credentials: 'same-origin',
                    body: formDataVerify
                })
                .then(response => response.json())
                .then(data => {
                    verifySmsBtn.disabled = false;
                    if (data.success) {
                        smsValidation.innerText = 'Phone number verified successfully!';
                        smsValidation.className = 'sms-validation valid';
                        smsVerified = true;
                        regSmsCode.disabled = true;
                        verifySmsBtn.disabled = true;
                        resetSmsBtn.style.display = 'none';
                    } else {
                        smsValidation.innerText = data.message || 'Incorrect verification code';
                        smsValidation.className = 'sms-validation invalid';
                    }
                })
                .catch(() => {
                    verifySmsBtn.disabled = false;
                    smsValidation.innerText = 'Network error. Please check your connection and try again.';
                    smsValidation.className = 'sms-validation invalid';
                });
            });
            
            resetSmsBtn.addEventListener('click', () => {
                smsVerified = false;
                regPhone.disabled = false;
                regPhone.value = '';
                regSmsCode.value = '';
                regSmsCode.disabled = false;
                regSmsCode.style.display = 'none';
                verifySmsBtn.disabled = false;
                verifySmsBtn.style.display = 'none';
                resetSmsBtn.style.display = 'none';
                sendSmsBtn.style.display = 'inline-block';
                
                smsValidation.innerText = '';
                smsValidation.className = 'sms-validation';
                phoneValidation.innerText = '';
                phoneValidation.className = 'phone-validation';
                
                regPhone.focus();
            });
        });
    </script>
</body>
</html>