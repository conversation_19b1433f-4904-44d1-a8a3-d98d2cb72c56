-- =============================================
-- KelvinKMS - Complete Database Setup
-- =============================================

-- Drop existing database if exists
DROP DATABASE IF EXISTS kelvinkms;
CREATE DATABASE kelvinkms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE kelvinkms;

-- Users table for authentication with extended profile
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(50),
    last_name VARCHA<PERSON>(50),
    nickname VA<PERSON><PERSON><PERSON>(50),
    gender ENUM('male', 'female', 'other', 'prefer_not_to_say') DEFAULT 'prefer_not_to_say',
    birthday DATE,
    language VARCHAR(10) DEFAULT 'en',
    email VARCHAR(100) UNIQUE,
    phone_number VA<PERSON>HAR(20),
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    last_seen TIMESTAMP NULL DEFAULT NULL,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_is_active (is_active)
);

-- User Wallets Table - Stores each user's credit balance
CREATE TABLE IF NOT EXISTS user_wallets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL UNIQUE,
    balance DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    frozen_balance DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Balance locked during pending transactions',
    total_deposited DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Total amount ever deposited',
    total_spent DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Total amount ever spent',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_balance (balance)
);

-- Credit Transactions Table - Records all credit-related transactions
CREATE TABLE IF NOT EXISTS credit_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_id VARCHAR(50) UNIQUE NOT NULL COMMENT 'Unique transaction identifier',
    user_id INT NOT NULL,
    transaction_type ENUM('deposit', 'withdraw', 'spend', 'refund', 'transfer_in', 'transfer_out', 'admin_adjust', 'admin_gift', 'admin_deduct') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    balance_before DECIMAL(10,2) NOT NULL,
    balance_after DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending',
    description TEXT,
    reference_type ENUM('order', 'deposit', 'transfer', 'admin', 'refund') DEFAULT NULL,
    reference_id VARCHAR(50) DEFAULT NULL COMMENT 'ID of related order, transfer, etc.',
    ip_address VARCHAR(45) DEFAULT NULL,
    metadata JSON DEFAULT NULL COMMENT 'Additional transaction data',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Orders table for service orders with pricing
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    user_id INT NOT NULL,
    username VARCHAR(50) NOT NULL,
    services JSON NOT NULL,
    notes TEXT,
    budget_range VARCHAR(50),
    estimated_price DECIMAL(10,2) DEFAULT NULL,
    final_price DECIMAL(10,2) DEFAULT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status ENUM('pending', 'processing', 'completed', 'cancelled', 'refunded') DEFAULT 'pending',
    payment_status ENUM('pending', 'paid', 'partially_paid', 'refunded', 'failed') DEFAULT 'pending',
    payment_method VARCHAR(50) DEFAULT NULL,
    payment_reference VARCHAR(100) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL DEFAULT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_payment_status (payment_status),
    INDEX idx_created_at (created_at)
);

-- Affiliate System Tables
CREATE TABLE IF NOT EXISTS affiliate_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL UNIQUE,
    affiliate_code VARCHAR(20) NOT NULL UNIQUE,
    is_active BOOLEAN DEFAULT TRUE,
    total_referrals INT DEFAULT 0,
    total_commissions DECIMAL(10,2) DEFAULT 0.00,
    total_withdrawn DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_affiliate_code (affiliate_code),
    INDEX idx_user_id (user_id),
    INDEX idx_active (is_active)
);

CREATE TABLE IF NOT EXISTS affiliate_referrals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    referrer_id INT NOT NULL COMMENT 'User who made the referral',
    referred_id INT NOT NULL COMMENT 'User who was referred',
    affiliate_code VARCHAR(20) NOT NULL,
    referral_source VARCHAR(100) DEFAULT NULL COMMENT 'Where the referral came from',
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    confirmed_at TIMESTAMP NULL DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referred_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (affiliate_code) REFERENCES affiliate_codes(affiliate_code) ON DELETE CASCADE,
    UNIQUE KEY unique_referral (referred_id), -- Each user can only be referred once
    INDEX idx_referrer (referrer_id),
    INDEX idx_referred (referred_id),
    INDEX idx_affiliate_code (affiliate_code),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

CREATE TABLE IF NOT EXISTS affiliate_commissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    referrer_id INT NOT NULL,
    referred_id INT NOT NULL,
    order_id INT DEFAULT NULL COMMENT 'Related order that triggered commission',
    commission_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    commission_type ENUM('referral_bonus', 'order_commission', 'special_bonus') DEFAULT 'referral_bonus',
    status ENUM('pending', 'paid', 'cancelled') DEFAULT 'pending',
    transaction_id VARCHAR(50) DEFAULT NULL COMMENT 'Related credit transaction ID',
    notes TEXT DEFAULT NULL,
    paid_at TIMESTAMP NULL DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referred_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL,
    INDEX idx_referrer (referrer_id),
    INDEX idx_referred (referred_id),
    INDEX idx_status (status),
    INDEX idx_commission_type (commission_type),
    INDEX idx_created_at (created_at)
);

-- Chat System Tables
CREATE TABLE IF NOT EXISTS chat_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(50) UNIQUE NOT NULL,
    user_id INT NOT NULL,
    admin_id INT,
    status ENUM('waiting', 'active', 'closed') DEFAULT 'waiting',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    closed_at TIMESTAMP NULL DEFAULT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_admin_id (admin_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

CREATE TABLE IF NOT EXISTS chat_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(50) NOT NULL,
    sender_id INT NOT NULL,
    message_type ENUM('text', 'image', 'file', 'system') DEFAULT 'text',
    message TEXT NOT NULL,
    attachment_url VARCHAR(255) DEFAULT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES chat_sessions(session_id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_sender_id (sender_id),
    INDEX idx_created_at (created_at)
);

-- System Settings Table
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description TEXT DEFAULT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    updated_by INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_setting_key (setting_key)
);

-- View counter table for tracking page views
CREATE TABLE IF NOT EXISTS view_counter (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_name VARCHAR(100) DEFAULT 'index',
    view_count INT DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_page (page_name)
);

-- Initialize view counter for index page
INSERT INTO view_counter (page_name, view_count) VALUES ('index', 0) 
ON DUPLICATE KEY UPDATE view_count = view_count;

-- Insert default admin user (password: admin123)
INSERT INTO users (username, password, email, first_name, last_name, is_verified, is_active, is_admin) 
VALUES ('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'System', 'Administrator', TRUE, TRUE, TRUE)
ON DUPLICATE KEY UPDATE 
    password = VALUES(password),
    is_active = VALUES(is_active),
    is_admin = VALUES(is_admin);

-- Insert initial wallet for admin
INSERT INTO user_wallets (user_id, balance) 
SELECT 1, 1000.00 FROM DUAL 
WHERE NOT EXISTS (SELECT 1 FROM user_wallets WHERE user_id = 1);

-- Insert initial affiliate code for admin
INSERT INTO affiliate_codes (user_id, affiliate_code, is_active) 
SELECT 1, 'ADMIN123', TRUE FROM DUAL 
WHERE NOT EXISTS (SELECT 1 FROM affiliate_codes WHERE user_id = 1);

-- Insert essential system settings
INSERT INTO system_settings (setting_key, setting_value, description, is_public) VALUES
('site_name', 'KelvinKMS', 'The name of the website', TRUE),
('site_email', '<EMAIL>', 'Default email address for system emails', TRUE),
('default_currency', 'USD', 'Default currency for the system', TRUE),
('referral_bonus', '50.00', 'Default referral bonus amount', FALSE),
('min_withdrawal', '100.00', 'Minimum withdrawal amount', TRUE),
('max_file_upload_size', '5242880', 'Maximum file upload size in bytes (5MB)', TRUE)
ON DUPLICATE KEY UPDATE 
    setting_value = VALUES(setting_value),
    description = VALUES(description),
    is_public = VALUES(is_public);

-- Create necessary database users and permissions (adjust passwords in production)
-- Note: These commands are commented out for security - uncomment and modify as needed
-- CREATE USER IF NOT EXISTS 'kelvinkms_user'@'localhost' IDENTIFIED BY 'strong_password_here';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON kelvinkms.* TO 'kelvinkms_user'@'localhost';
-- FLUSH PRIVILEGES;

-- Create database indexes for better performance
CREATE INDEX IF NOT EXISTS idx_orders_user_id_status ON orders(user_id, status);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_user_id_type ON credit_transactions(user_id, transaction_type);
CREATE INDEX IF NOT EXISTS idx_affiliate_commissions_referrer_status ON affiliate_commissions(referrer_id, status);
CREATE INDEX IF NOT EXISTS idx_chat_messages_session_created ON chat_messages(session_id, created_at);
