<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fixes - KelvinKMS.com</title>
    <link rel="stylesheet" href="CSS/style.css">
    <style>
        body {
            background-color: #a48f19;
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: auto;
            background-color: rgb(5 195 182);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        h1 {
            color: #ffffff;
            text-align: center;
            font-size: 32px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }
        .test-section h2 {
            color: #ffffff;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
            display: flex;
            flex-direction: column;
        }
        .form-group label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #ffffff;
            font-size: 18px;
        }
        .form-group input {
            padding: 10px;
            border: 2px solid rgb(253, 202, 0);
            border-radius: 12px;
            font-size: 16px;
            background: rgb(255 194 0);
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        .form-group input:focus {
            outline: none;
            border-color: rgba(255, 213, 0, 0.8);
            background: rgba(255, 196, 0, 0.95);
        }
        .phone-input-container {
            display: flex;
            align-items: center;
            position: relative;
        }
        .phone-prefix {
            background: rgb(255 194 0);
            border: 2px solid rgb(253, 202, 0);
            border-right: none;
            border-radius: 12px 0 0 12px;
            padding: 10px 12px;
            font-weight: bold;
            color: #000;
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        .phone-input-container input {
            border-left: none;
            border-radius: 0 12px 12px 0;
            flex: 1;
        }
        .validation-message {
            margin-top: 10px;
            padding: 10px;
            border-radius: 12px;
            font-weight: bold;
        }
        .validation-message.valid {
            background: rgb(0 90 4);
            color: #ffffff;
        }
        .validation-message.invalid {
            background: rgb(168 12 0);
            color: #ffffff;
        }
        .btn {
            padding: 10px 20px;
            border: 2px solid rgb(253, 202, 0);
            border-radius: 12px;
            background: rgb(253, 202, 0);
            color: #000;
            cursor: pointer;
            font-weight: bold;
            box-shadow: 0 2px 8px rgb(0 0 0);
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: rgba(255, 196, 0, 0.95);
            border-color: rgba(255, 213, 0, 0.8);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test Fixes</h1>
        
        <!-- Test 1: Email Confirmation -->
        <div class="test-section">
            <h2>1. Email Confirmation Test</h2>
            <div class="form-group">
                <label for="testEmail">Email:</label>
                <input type="email" id="testEmail" placeholder="Enter your email">
                <div class="validation-message" id="emailValidation"></div>
            </div>
            <div class="form-group">
                <label for="testEmailConfirm">Confirm Email:</label>
                <input type="email" id="testEmailConfirm" placeholder="Confirm your email">
                <div class="validation-message" id="emailConfirmValidation"></div>
            </div>
        </div>
        
        <!-- Test 2: Phone Number Input -->
        <div class="test-section">
            <h2>2. Phone Number Input Test</h2>
            <div class="form-group">
                <label for="testPhone">Phone Number:</label>
                <div class="phone-input-container">
                    <span class="phone-prefix">+1</span>
                    <input type="tel" id="testPhone" placeholder="Enter 10-digit phone number" maxlength="14">
                </div>
                <div class="validation-message" id="phoneValidation"></div>
            </div>
            <button class="btn" onclick="testPhoneFormat()">Test Phone Format</button>
        </div>
        
        <!-- Test 3: Admin Links -->
        <div class="test-section">
            <h2>3. Admin Page Links Test</h2>
            <p>Test the updated admin page styles:</p>
            <button class="btn" onclick="window.open('PHP/admin.php', '_blank')">Open Admin Page</button>
            <button class="btn" onclick="window.open('PHP/admin_member_management.php', '_blank')">Open Member Management</button>
            <button class="btn" onclick="window.open('PHP/admin_orders.php', '_blank')">Open PC Orders Management</button>
        </div>
        
        <!-- Test 4: Member Account Settings -->
        <div class="test-section">
            <h2>4. Member Account Settings Test</h2>
            <p>Test the new account settings page (requires login):</p>
            <button class="btn" onclick="window.open('PHP/member.php', '_blank')">Open Member Page</button>
            <button class="btn" onclick="window.open('PHP/account_settings.php', '_blank')">Open Account Settings</button>
        </div>
    </div>

    <script>
        // Simplified i18n object for testing
        const i18n = {
            js_form_invalid_email_format: 'Invalid email format.',
            js_form_valid_email_format: 'Valid email format.',
            js_form_email_mismatch: 'Email addresses do not match.',
            js_form_email_match: 'E-mail match.'
        };

        // Email validation function
        function validateEmail(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        }

        // Phone validation function
        function validateUSPhoneNumber(phone) {
            const digits = phone.replace(/\D/g, '');
            if (digits.length === 10) {
                return digits.match(/^[2-9]\d{2}[2-9]\d{2}\d{4}$/);
            }
            return false;
        }

        // Format phone number input
        function formatPhoneInput(input) {
            let value = input.value.replace(/\D/g, '');
            
            if (value.length > 10) {
                value = value.substring(0, 10);
            }
            
            let formattedValue = '';
            if (value.length > 0) {
                if (value.length <= 3) {
                    formattedValue = `(${value}`;
                } else if (value.length <= 6) {
                    formattedValue = `(${value.substring(0, 3)}) ${value.substring(3)}`;
                } else {
                    formattedValue = `(${value.substring(0, 3)}) ${value.substring(3, 6)}-${value.substring(6)}`;
                }
            }
            
            input.value = formattedValue;
        }

        // Email validation handlers
        const testEmail = document.getElementById('testEmail');
        const testEmailConfirm = document.getElementById('testEmailConfirm');
        const emailValidation = document.getElementById('emailValidation');
        const emailConfirmValidation = document.getElementById('emailConfirmValidation');

        function updateEmailValidation() {
            const email = testEmail.value.trim();
            if (!email) {
                emailValidation.innerText = '';
                emailValidation.className = 'validation-message';
            } else if (!validateEmail(email)) {
                emailValidation.innerText = i18n.js_form_invalid_email_format;
                emailValidation.className = 'validation-message invalid';
            } else {
                emailValidation.innerText = i18n.js_form_valid_email_format;
                emailValidation.className = 'validation-message valid';
            }
        }

        function updateEmailConfirmValidation() {
            const email = testEmail.value.trim();
            const confirm = testEmailConfirm.value.trim();
            if (!confirm) {
                emailConfirmValidation.innerText = '';
                emailConfirmValidation.className = 'validation-message';
            } else if (!validateEmail(email)) {
                emailConfirmValidation.innerText = i18n.js_form_invalid_email_format;
                emailConfirmValidation.className = 'validation-message invalid';
            } else if (email !== confirm) {
                emailConfirmValidation.innerText = i18n.js_form_email_mismatch;
                emailConfirmValidation.className = 'validation-message invalid';
            } else {
                emailConfirmValidation.innerText = i18n.js_form_email_match;
                emailConfirmValidation.className = 'validation-message valid';
            }
        }

        // Phone validation handlers
        const testPhone = document.getElementById('testPhone');
        const phoneValidation = document.getElementById('phoneValidation');

        function updatePhoneValidation() {
            const phone = testPhone.value.trim();
            
            if (!phone) {
                phoneValidation.innerText = '';
                phoneValidation.className = 'validation-message';
                return;
            }
            
            if (!validateUSPhoneNumber(phone)) {
                phoneValidation.innerText = 'Please enter a valid 10-digit US phone number';
                phoneValidation.className = 'validation-message invalid';
            } else {
                phoneValidation.innerText = 'Valid US phone number format';
                phoneValidation.className = 'validation-message valid';
            }
        }

        function testPhoneFormat() {
            const phone = testPhone.value.trim();
            const digits = phone.replace(/\D/g, '');
            const formatted = digits.length === 10 ? `+1${digits}` : phone;
            alert(`Original: ${phone}\nDigits only: ${digits}\nFormatted for SMS: ${formatted}`);
        }

        // Event listeners
        if (testEmail && testEmailConfirm) {
            testEmail.addEventListener('input', updateEmailValidation);
            testEmail.addEventListener('input', updateEmailConfirmValidation);
            testEmailConfirm.addEventListener('input', updateEmailConfirmValidation);
        }

        if (testPhone) {
            testPhone.addEventListener('input', function() {
                formatPhoneInput(this);
                updatePhoneValidation();
            });
        }
    </script>
</body>
</html>
