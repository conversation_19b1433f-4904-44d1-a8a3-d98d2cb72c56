<?php
// Test SMS verification functionality
session_start();
header('Content-Type: application/json');

echo "Testing SMS verification...\n";

// Test 1: Send SMS verification code
echo "Test 1: Sending SMS verification code\n";
$_POST['phone_number'] = '+1234567890';
$_SERVER['REQUEST_METHOD'] = 'POST';

// Clear any existing session data
unset($_SESSION['sms_verification_code']);
unset($_SESSION['sms_phone_number']);
unset($_SESSION['sms_code_time']);

// Capture output from send_sms_verification.php
ob_start();
include 'send_sms_verification.php';
$output = ob_get_clean();

echo "SMS Send Response: " . $output . "\n";
$response = json_decode($output, true);

if ($response && $response['success']) {
    echo "✓ SMS sending works correctly!\n";
    
    // Test 2: Verify the code
    echo "\nTest 2: Verifying SMS code\n";
    
    if (isset($response['demo_code'])) {
        echo "Demo code: " . $response['demo_code'] . "\n";
        
        // Test verification with correct code
        $_POST = [];
        $_POST['verification_code'] = $response['demo_code'];
        
        ob_start();
        include 'verify_sms_code.php';
        $verify_output = ob_get_clean();
        
        echo "SMS Verify Response: " . $verify_output . "\n";
        $verify_response = json_decode($verify_output, true);
        
        if ($verify_response && $verify_response['success']) {
            echo "✓ SMS verification works correctly!\n";
        } else {
            echo "✗ SMS verification failed\n";
        }
    } else {
        echo "No demo code provided, testing with session data\n";
        if (isset($_SESSION['sms_verification_code'])) {
            echo "Session verification code: " . $_SESSION['sms_verification_code'] . "\n";
        }
    }
} else {
    echo "✗ SMS sending failed: " . ($response['message'] ?? 'Unknown error') . "\n";
}

echo "\nSession data:\n";
foreach ($_SESSION as $key => $value) {
    if (strpos($key, 'sms_') === 0) {
        echo "  $key: $value\n";
    }
}

echo "\nTest completed!\n";
?>