body {
    font-family: sans-serif;
    background-color: #1599c7;
    margin: 0;
    padding: 0;
}

.container {
    max-width: 60%;
    margin: auto;
    background: #3186b7;
    border-radius: 12px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    text-align: center;
}

.view-counter {
    background: #24b91f;
    color: #fff;
    padding: 5px 10px;
    border-radius: 12px;
    font-size: 20px;
    display: inline-block;
    margin-bottom: 10px;
}

.top-left-auth {
    margin-bottom: 20px;
}

.top-left-auth button {
    padding: 10px 15px;
    border: none;
    background-color: #333;
    color: white;
    cursor: pointer;
    border-radius: 5px;
    margin-right: 10px;
}

.top-left-auth button:hover {
    background-color: #555;
}

.register-modal-content {
    background-color: rgb(5 195 182);
    padding: 20px;
    width: 600px;
    max-width: 600px;
    max-height: 90vh;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgb(0 0 0);
    animation-name: animatetop;
    animation-duration: 0.4s;
    align-items: center;
    text-align: center;
    color: white;
    overflow-y: auto;
}

/* Mobile responsive for register modal */
@media (max-width: 768px) {
    .register-modal-content {
        width: 100%;
        height: 100vh;
        max-height: 100vh;
        margin: 0;
        border-radius: 0;
        padding: 16px;
        overflow-y: auto;
    }
}

/* Add Animation */
@keyframes animatetop {
    from {top: -300px; opacity: 0}
    to {top: 0; opacity: 1}
}

.modal h2 {
    text-align: center;
}

.modal form {
    display: flex;
    flex-direction: column;
}

/* Form Group Styling */
.form-group {
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: bold;
    margin-bottom: 0px;
    color: #ffffff;
    font-size: 24px;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="tel"],
.form-group input[type="date"],
.form-group input[type="password"] {
    padding: 10px;
    border: 2px solid rgb(253, 202, 0);
    border-radius: 12px;
    font-size: 24px;
    background: rgb(255 194 0);
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.form-group input:focus {
    outline: none;
/* Form row for first and last name fields */
.form-row {
    display: flex;
    gap: 5px;
}
.form-row .form-group {
    flex: 1;
    margin-bottom: 15px;
}
.form-row .form-group input {
    width: 100%;
}
    border-color: rgba(255, 213, 0, 0.8);
    background: rgba(255, 196, 0, 0.95);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

/* Gender Buttons */
.gender-buttons {
    display: flex;
    gap: 15px;
    margin-top: 8px;
}

.gender-btn {
    flex: 1;
    padding: 15px;
    border: 2px solid rgba(164, 179, 255, 0.3);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.gender-btn:hover {
    border-color: rgb(0, 208, 255);
    background: rgb(0, 255, 251);
}

.gender-btn.selected {
    /* Gold highlight to indicate selected state */
    border-color: #FFD700;
    background: rgb(255 215 0);
    color: #ffffff;
}

.gender-icon {
    font-size: 24px;
    font-weight: bold;
}

/* Language Buttons */
.language-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-top: 8px;
}

.lang-btn {
    padding: 12px;
    border: 2px solid rgba(164, 179, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.lang-btn:hover {
    border-color: rgba(164, 179, 255, 0.6);
    background: rgba(255, 255, 255, 0.9);
}

.lang-btn.selected {
    /* Gold highlight to indicate selected state */
    border-color: #FFD700;
    background: rgb(255, 217, 0);
    color: #ffffff;
}

/* Mobile responsive for language buttons */
@media (max-width: 768px) {
    .language-buttons {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Username Validation */
.username-hint {
    font-size: 22px;
    color: #ffffff;
    margin-top: 5px;
}

.username-validation {
    margin-top: 8px;
    font-size: 20px;
    padding: 8px 12px 8px 45px;
    border-radius: 12px;
    display: block;
    transition: all 0.3s ease;
    min-height: 22px;
    position: relative;
    line-height: 24px;
    z-index: 10;
}

.username-validation.checking {
    color: #ffffff;
    background: rgb(0, 140, 255);
    border: 2px solid rgba(33, 150, 243, 0.5);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.username-validation.checking::before {
    content: "👤⏳";
    font-size: 18px;
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 11;
    display: block;
}

.username-validation.error {
    color: #ffffff;
    background: rgb(168 12 0);
    border: 2px solid rgba(255, 152, 0, 0.5);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.username-validation.error::before {
    content: "👤⚠️";
    font-size: 18px;
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 11;
    display: block;
}

.username-validation.valid {
    color: #ffffff;
    background: rgb(0 90 4);
    border: 2px solid rgba(76, 175, 80, 0.5);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.username-validation.valid::before {
    content: "👤✅";
    font-size: 18px;
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 11;
    display: block;
}

.username-validation.invalid {
    color: #ffffff;
    background: rgb(168 12 0);
    border: 2px solid rgba(244, 67, 54, 0.5);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.username-validation.invalid::before {
    content: "👤❌";
    font-size: 18px;
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 11;
    display: block;
}

/* Age Validation */
.age-validation {
    margin-top: 8px;
    font-size: 20px;
    padding: 8px 12px 8px 45px;
    border-radius: 12px;
    display: block;
    transition: all 0.3s ease;
    min-height: 22px;
    position: relative;
    line-height: 24px;
}

.age-validation.valid {
    color: #ffffff;
    background: rgb(0 90 4);
    border: 2px solid rgba(76, 175, 80, 0.5);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.age-validation.valid::before {
    content: "🎂✅";
    font-size: 18px;
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
}

.age-validation.invalid {
    color: #ffffff;
    background: rgb(168 12 0);
    border: 2px solid rgba(244, 67, 54, 0.5);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.age-validation.invalid::before {
    content: "🎂❌";
    font-size: 18px;
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
}

/* Email Validation */
.email-validation {
    margin-top: 8px;
    font-size: 20px;
    padding: 8px 12px 8px 45px;
    border-radius: 12px;
    display: block;
    transition: all 0.3s ease;
    min-height: 22px;
    position: relative;
    line-height: 24px;
}

.email-validation.valid {
    color: #ffffff;
    background: rgb(0 90 4);
    border: 2px solid rgba(76, 175, 80, 0.5);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.email-validation.valid::before {
    content: "✉️✅";
    font-size: 18px;
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
}

.email-validation.invalid {
    color: #ffffff;
    background: rgb(160 11 0);
    border: 2px solid rgba(244, 67, 54, 0.5);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.email-validation.invalid::before {
    content: "✉️❌";
    font-size: 18px;
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
}

/* Phone Input Group */
.phone-input-group {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.phone-input-group input[type="tel"] {
    flex: 1;
    min-width: 200px;
    padding: 10px;
    border: 2px solid rgb(253, 202, 0);
    border-radius: 12px;
    font-size: 24px;
    background: rgb(255 194 0);
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.phone-input-group input[type="tel"]:focus {
    outline: none;
    border-color: rgba(255, 213, 0, 0.8);
    background: rgba(255, 196, 0, 0.95);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.phone-input-group input[type="text"] {
    flex: 1;
    min-width: 120px;
    padding: 10px;
    border: 2px solid rgb(253, 202, 0);
    border-radius: 12px;
    font-size: 24px;
    background: rgb(255 194 0);
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.sms-btn, .verify-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 12px;
    background: rgba(164, 179, 255, 0.8);
    color: white;
    cursor: pointer;
    font-size: 20px;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-width: 80px;
}

.sms-btn:hover, .verify-btn:hover {
    background: rgba(164, 179, 255, 1);
}

.sms-btn:disabled, .verify-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* Phone Format Hint */
.phone-format-hint {
    margin-top: 5px;
    font-size: 18px;
    color: #ffffff;
    background: rgba(0, 100, 200, 0.8);
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid rgba(164, 179, 255, 0.3);
}

/* Phone Validation */
.phone-validation {
    margin-top: 8px;
    font-size: 20px;
    padding: 8px 12px 8px 45px;
    border-radius: 12px;
    display: block;
    transition: all 0.3s ease;
    min-height: 22px;
    position: relative;
    line-height: 24px;
    z-index: 10;
}

.phone-validation.valid {
    color: #ffffff;
    background: rgb(0 90 4);
    border: 2px solid rgba(76, 175, 80, 0.5);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.phone-validation.valid::before {
    content: "📱✅";
    font-size: 18px;
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 11;
    display: block;
}

.phone-validation.invalid {
    color: #ffffff;
    background: rgb(168 12 0);
    border: 2px solid rgba(244, 67, 54, 0.5);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.phone-validation.invalid::before {
    content: "📱❌";
    font-size: 18px;
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 11;
    display: block;
}

.phone-validation.checking {
    color: #ffffff;
    background: rgb(0, 140, 255);
    border: 2px solid rgba(33, 150, 243, 0.5);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.phone-validation.checking::before {
    content: "📱⏳";
    font-size: 18px;
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 11;
    display: block;
}

/* SMS Verification */
.sms-verification {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.sms-verification input {
    flex: 1;
    text-align: center;
    font-size: 20px;
    letter-spacing: 2px;
}

.sms-status {
    margin-top: 8px;
    font-size: 20px;
    padding: 8px 12px 8px 45px;
    border-radius: 12px;
    display: block;
    transition: all 0.3s ease;
    min-height: 22px;
    position: relative;
    line-height: 24px;
}

.sms-status.success {
    color: #ffffff;
    background: rgb(0 90 4);
    border: 2px solid rgba(76, 175, 80, 0.5);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.sms-status.success::before {
    content: "📱✅";
    font-size: 20px;
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
}

.sms-status.error {
    color: #ffffff;
    background: rgb(168 12 0);
    border: 2px solid rgba(244, 67, 54, 0.5);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.sms-status.error::before {
    content: "📱❌";
    font-size: 20px;
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
}

.sms-status.info {
    color: #ffffff;
    background: rgb(0 90 4);
    border: 2px solid rgba(33, 150, 243, 0.5);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.sms-status.info::before {
    content: "📱📤";
    font-size: 18px;
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
}

/* Password Requirements */
.password-requirements {
    margin-top: 10px;
    padding: 15px;
    background: rgb(255 0 80);
    border-radius: 12px;
    border: 1px solid rgba(164, 179, 255, 0.3);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.requirement-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #ffffff;
}

.requirement {
    font-size: 22px;
    margin: 4px 0;
    color: #ffffff;
    transition: all 0.3s ease;
    padding: 8px 12px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    border: 1px solid transparent;
}

.requirement.valid {
    color: #ffffff;
    background: rgb(19 84 10);
    border: 1px solid rgba(76, 175, 80, 0.4);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.requirement.valid::before {
    content: "🔐✅";
    font-size: 16px;
    margin-right: 4px;
}

.requirement.invalid {
    color: #ffffff;
    background: rgb(168 12 0);
    border: 1px solid rgba(244, 67, 54, 0.4);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.requirement.invalid::before {
    content: "🔐❌";
    font-size: 20px;
    margin-right: 4px;
}

/* Password Match */
.password-match {
    margin-top: 8px;
    font-size: 20px;
    padding: 8px 12px 8px 45px;
    border-radius: 12px;
    display: block;
    transition: all 0.3s ease;
    min-height: 22px;
    position: relative;
    line-height: 24px;
}

.password-match.valid {
    color: #ffffff;
    background: rgb(0 96 4);
    border: 2px solid rgba(76, 175, 80, 0.5);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.password-match.valid::before {
    content: "🔒✅";
    font-size: 20px;
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
}

.password-match.invalid {
    color: #ffffff;
    background: rgb(179 12 0);
    border: 2px solid rgba(244, 67, 54, 0.5);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.password-match.invalid::before {
    content: "🔒❌";
    font-size: 20px;
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
}

/* Register Submit Button */
.register-submit-btn {
    width: 100%;
    padding: 15px;
    margin-top: 20px;
    border: none;
    border-radius: 12px;
    background: #e9ba00;
    color: white;
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.register-submit-btn:hover {
    background: linear-gradient(135deg, rgba(164, 179, 255, 1) 0%, rgba(255, 171, 188, 1) 100%);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.register-submit-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

h1, h2 {
    color: #ffffff;
    font-size: 28px;
}

a {
    color: #ffffff;
}

.alert {
    padding: 15px;
    background-color: #dff0d8;
    border-color: #d6e9c6;
    color: #3c763d;
    border-radius: 4px;
    margin-top: 20px;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

table, th, td {
    border: 1px solid #ddd;
}

th, td {
    padding: 10px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
}
/* Password strength progress bar */
.password-progress-wrapper {
    width: 100%;
    height: 8px;
    background: rgba(164, 179, 255, 0.3);
    border-radius: 12px;
    margin-top: 12px;
    overflow: hidden;
}

.password-progress {
    height: 100%;
    width: 0%;
    background: linear-gradient(90deg, #FF6B6B 0%, #FFD93D 50%, #4CAF50 100%);
    transition: width 0.3s ease;
}
/* —— Custom 4px Teal/Blue-Green Scrollbar —— */

/* WebKit Browsers */
::-webkit-scrollbar {
    width: 4px;
    height: 4px;
    background: transparent;
}
::-webkit-scrollbar-track,
::-webkit-scrollbar-corner,
::-webkit-scrollbar-track-piece {
    background: transparent;
}
::-webkit-scrollbar-thumb {
    background-color: #00a99d;
    border-radius: 0;
    border: none;
}
::-webkit-scrollbar-thumb:hover {
    background-color: #007f7a;
    border-radius: 0;
}

/* Firefox */
html {
    scrollbar-width: thin;
    scrollbar-color: #00a99d transparent;
}
/* Styled message bars on registration page */
.nickname-validation,
.username-validation,
.email-validation,
.password-match,
.age-validation,
.sms-validation,
.sms-status {
    margin-top: 10px;
    padding: 15px;
    background: rgb(255 0 80);
    border-radius: 12px;
    border: 1px solid rgba(164, 179, 255, 0.3);
    box-shadow: 0 2px 8px rgb(0 0 0);
}
.username-requirements {
    margin-top: 10px;
    padding: 15px;
    background: rgb(255 0 80);
    border-radius: 12px;
    border: 1px solid rgba(164, 179, 255, 0.3);
    box-shadow: 0 2px 8px rgb(0 0 0);
}