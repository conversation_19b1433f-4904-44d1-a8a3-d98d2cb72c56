<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || !$_SESSION["is_admin"]) {
    header("location: ../index.php");
    exit;
}

require_once 'config.php';
require_once 'credit_system.php';

// Initialize credit system
$credit_system = new KMSCreditSystem($link);

// Handle AJAX requests
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'get_members':
            getMembersList();
            break;
            
        case 'get_member_details':
            getMemberDetails($_POST['user_id']);
            break;
            
        case 'update_member':
            updateMemberProfile();
            break;
            
        case 'adjust_credit':
            adjustMemberCredit();
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
    }
    exit;
}

function getMembersList() {
    global $link;
    
    $sql = "SELECT u.id, u.username, u.first_name, u.last_name, u.nickname, u.email, u.phone_number, 
                   u.gender, u.birthday, u.language, u.created_at, u.last_login, u.is_active,
                   COALESCE(w.balance, 0) as credit_balance
            FROM users u 
            LEFT JOIN user_wallets w ON u.id = w.user_id 
            WHERE u.is_admin = FALSE 
            ORDER BY u.created_at DESC";
    
    $result = mysqli_query($link, $sql);
    
    if ($result) {
        $members = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $members[] = $row;
        }
        echo json_encode(['success' => true, 'members' => $members]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Error fetching members']);
    }
}

function getMemberDetails($user_id) {
    global $link;
    
    $sql = "SELECT u.*, COALESCE(w.balance, 0) as credit_balance, 
                   COALESCE(w.total_deposited, 0) as total_deposited,
                   COALESCE(w.total_spent, 0) as total_spent
            FROM users u 
            LEFT JOIN user_wallets w ON u.id = w.user_id 
            WHERE u.id = ?";
    
    $stmt = execute_query($link, $sql, "i", [$user_id]);
    
    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        $member = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if ($member) {
            echo json_encode(['success' => true, 'member' => $member]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Member not found']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
}

function updateMemberProfile() {
    global $link;
    
    $user_id = $_POST['user_id'];
    $first_name = trim($_POST['first_name']);
    $last_name = trim($_POST['last_name']);
    $nickname = trim($_POST['nickname']);
    $email = trim($_POST['email']);
    $phone_number = trim($_POST['phone_number']);
    $gender = $_POST['gender'];
    $birthday = $_POST['birthday'];
    $language = $_POST['language'];
    
    // Validate required fields
    if (empty($first_name) || empty($last_name) || empty($nickname) || empty($email)) {
        echo json_encode(['success' => false, 'message' => 'Required fields cannot be empty']);
        return;
    }
    
    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => 'Invalid email format']);
        return;
    }
    
    $sql = "UPDATE users SET first_name = ?, last_name = ?, nickname = ?, email = ?, 
            phone_number = ?, gender = ?, birthday = ?, language = ?, updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?";
    
    $stmt = execute_query($link, $sql, "ssssssssi", [
        $first_name, $last_name, $nickname, $email, $phone_number, 
        $gender, $birthday, $language, $user_id
    ]);
    
    if ($stmt) {
        mysqli_stmt_close($stmt);
        echo json_encode(['success' => true, 'message' => 'Member profile updated successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Error updating member profile']);
    }
}

function adjustMemberCredit() {
    global $credit_system;
    
    $user_id = $_POST['user_id'];
    $amount = floatval($_POST['amount']);
    $reason = trim($_POST['reason']);
    $admin_user_id = $_SESSION['id'];
    
    if ($amount == 0) {
        echo json_encode(['success' => false, 'message' => 'Amount cannot be zero']);
        return;
    }
    
    if (empty($reason)) {
        echo json_encode(['success' => false, 'message' => 'Reason is required']);
        return;
    }
    
    if ($amount > 0) {
        // Add credit (gift)
        $result = $credit_system->addCredit($user_id, $amount, 'admin_gift', $reason, null, $admin_user_id, 'admin_gift');
    } else {
        // Deduct credit
        $result = $credit_system->deductCredit($user_id, abs($amount), $reason, 'admin', null, $admin_user_id, 'admin_deduct');
    }
    
    echo json_encode($result);
}

close_db_connection($link);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Management - Admin Panel</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #a48f19;
            color: white;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: auto;
            background-color: rgb(5 195 182);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        h1 {
            color: #ffffff;
            text-align: center;
            margin-bottom: 30px;
            font-size: 32px;
        }
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            padding: 10px 20px;
            background: rgb(253, 202, 0);
            color: #000;
            border: 2px solid rgb(253, 202, 0);
            border-radius: 12px;
            cursor: pointer;
            text-decoration: none;
            font-weight: bold;
            box-shadow: 0 2px 8px rgb(0 0 0);
            transition: all 0.3s ease;
        }
        .back-btn:hover {
            background: rgba(255, 196, 0, 0.95);
            border-color: rgba(255, 213, 0, 0.8);
        }

        .search-section {
            margin-bottom: 20px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .search-section input {
            width: 300px;
            padding: 10px;
            border: 2px solid rgb(253, 202, 0);
            border-radius: 12px;
            margin-right: 10px;
            background: rgb(255 194 0);
            font-size: 16px;
            box-shadow: 0 2px 8px rgb(0 0 0);
        }

        .search-section input:focus {
            outline: none;
            border-color: rgba(255, 213, 0, 0.8);
            background: rgba(255, 196, 0, 0.95);
        }

        .btn {
            padding: 10px 20px;
            border: 2px solid;
            border-radius: 12px;
            cursor: pointer;
            margin-right: 10px;
            font-weight: bold;
            box-shadow: 0 2px 8px rgb(0 0 0);
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: rgb(253, 202, 0);
            color: #000;
            border-color: rgb(253, 202, 0);
        }
        .btn-primary:hover {
            background: rgba(255, 196, 0, 0.95);
            border-color: rgba(255, 213, 0, 0.8);
        }
        .btn-success {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }
        .btn-success:hover {
            background: #218838;
            border-color: #1e7e34;
        }
        .btn-warning {
            background: #ffc107;
            color: #000;
            border-color: #ffc107;
        }
        .btn-warning:hover {
            background: #e0a800;
            border-color: #d39e00;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
            border-color: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
            border-color: #bd2130;
        }

        .members-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgb(0 0 0);
        }

        .members-table th,
        .members-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .members-table th {
            background-color: rgba(0, 0, 0, 0.3);
            color: #ffffff;
            font-weight: bold;
            font-size: 16px;
        }

        .members-table tr:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: rgb(5 195 182);
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 80%;
            max-width: 600px;
            color: white;
            box-shadow: 0 2px 8px rgb(0 0 0);
            animation-name: animatetop;
            animation-duration: 0.4s;
        }

        @keyframes animatetop {
            from {top: -300px; opacity: 0}
            to {top: 0; opacity: 1}
        }

        .close {
            color: #ffffff;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover { color: #ffcccc; }

        .form-group {
            margin-bottom: 15px;
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #ffffff;
            font-weight: bold;
            font-size: 18px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid rgb(253, 202, 0);
            border-radius: 12px;
            background: rgb(255 194 0);
            color: #000;
            font-size: 16px;
            box-shadow: 0 2px 8px rgb(0 0 0);
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: rgba(255, 213, 0, 0.8);
            background: rgba(255, 196, 0, 0.95);
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #00ffff;
        }
    </style>
</head>
<body>
    <a href="admin.php" class="back-btn">← Back to Admin</a>
    
    <div class="container">
        <h1>👥 Member Management</h1>
        
        <div class="search-section">
            <input type="text" id="searchInput" placeholder="Search by username, email, or name...">
            <button class="btn btn-primary" onclick="loadMembers()">🔍 Search</button>
            <button class="btn btn-success" onclick="loadMembers()">🔄 Refresh</button>
        </div>
        
        <div id="membersContainer">
            <div class="loading">Loading members...</div>
        </div>
    </div>
    
    <!-- Edit Member Modal -->
    <div id="editMemberModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('editMemberModal')">&times;</span>
            <h2>Edit Member Profile</h2>
            <form id="editMemberForm">
                <input type="hidden" id="editUserId" name="user_id">
                
                <div class="form-group">
                    <label for="editFirstName">First Name:</label>
                    <input type="text" id="editFirstName" name="first_name" required>
                </div>
                
                <div class="form-group">
                    <label for="editLastName">Last Name:</label>
                    <input type="text" id="editLastName" name="last_name" required>
                </div>
                
                <div class="form-group">
                    <label for="editNickname">Nickname:</label>
                    <input type="text" id="editNickname" name="nickname" required>
                </div>
                
                <div class="form-group">
                    <label for="editEmail">Email:</label>
                    <input type="email" id="editEmail" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="editPhone">Phone Number:</label>
                    <input type="text" id="editPhone" name="phone_number">
                </div>
                
                <div class="form-group">
                    <label for="editGender">Gender:</label>
                    <select id="editGender" name="gender">
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                        <option value="prefer_not_to_say">Prefer not to say</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="editBirthday">Birthday:</label>
                    <input type="date" id="editBirthday" name="birthday">
                </div>
                
                <div class="form-group">
                    <label for="editLanguage">Language:</label>
                    <select id="editLanguage" name="language">
                        <option value="en">English</option>
                        <option value="zh">中文</option>
                        <option value="es">Español</option>
                        <option value="ja">日本語</option>
                        <option value="ko">한국어</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                
                <button type="submit" class="btn btn-primary">Update Profile</button>
                <button type="button" class="btn btn-warning" onclick="closeModal('editMemberModal')">Cancel</button>
            </form>
        </div>
    </div>
    
    <!-- Credit Adjustment Modal -->
    <div id="creditModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('creditModal')">&times;</span>
            <h2>Adjust KMS Credit</h2>
            <form id="creditForm">
                <input type="hidden" id="creditUserId" name="user_id">
                
                <div class="form-group">
                    <label for="creditAmount">Amount (use negative for deduction):</label>
                    <input type="number" id="creditAmount" name="amount" step="0.01" required>
                </div>
                
                <div class="form-group">
                    <label for="creditReason">Reason:</label>
                    <textarea id="creditReason" name="reason" rows="3" required placeholder="Enter reason for credit adjustment..."></textarea>
                </div>
                
                <button type="submit" class="btn btn-success">Adjust Credit</button>
                <button type="button" class="btn btn-warning" onclick="closeModal('creditModal')">Cancel</button>
            </form>
        </div>
    </div>

    <script>
        // Load members on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadMembers();
        });

        function loadMembers() {
            const searchTerm = document.getElementById('searchInput').value;
            
            fetch('admin_member_management.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=get_members&search=' + encodeURIComponent(searchTerm)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayMembers(data.members);
                } else {
                    document.getElementById('membersContainer').innerHTML = '<div class="loading">Error loading members: ' + data.message + '</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('membersContainer').innerHTML = '<div class="loading">Network error occurred</div>';
            });
        }

        function displayMembers(members) {
            let html = `
                <table class="members-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Username</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Credit Balance</th>
                            <th>Joined</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            members.forEach(member => {
                html += `
                    <tr>
                        <td>${member.id}</td>
                        <td>${member.username}</td>
                        <td>${member.first_name} ${member.last_name}</td>
                        <td>${member.email}</td>
                        <td>$${parseFloat(member.credit_balance).toFixed(2)}</td>
                        <td>${new Date(member.created_at).toLocaleDateString()}</td>
                        <td>
                            <button class="btn btn-primary" onclick="editMember(${member.id})">Edit</button>
                            <button class="btn btn-success" onclick="adjustCredit(${member.id})">Credit</button>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            document.getElementById('membersContainer').innerHTML = html;
        }

        function editMember(userId) {
            // Fetch member details
            fetch('admin_member_management.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=get_member_details&user_id=' + userId
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const member = data.member;
                    
                    // Populate form
                    document.getElementById('editUserId').value = member.id;
                    document.getElementById('editFirstName').value = member.first_name || '';
                    document.getElementById('editLastName').value = member.last_name || '';
                    document.getElementById('editNickname').value = member.nickname || '';
                    document.getElementById('editEmail').value = member.email || '';
                    document.getElementById('editPhone').value = member.phone_number || '';
                    document.getElementById('editGender').value = member.gender || 'prefer_not_to_say';
                    document.getElementById('editBirthday').value = member.birthday || '';
                    document.getElementById('editLanguage').value = member.language || 'en';
                    
                    // Show modal
                    document.getElementById('editMemberModal').style.display = 'block';
                } else {
                    alert('Error loading member details: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Network error occurred');
            });
        }

        function adjustCredit(userId) {
            document.getElementById('creditUserId').value = userId;
            document.getElementById('creditModal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // Handle edit member form submission
        document.getElementById('editMemberForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            formData.append('action', 'update_member');
            
            fetch('admin_member_management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Member profile updated successfully!');
                    closeModal('editMemberModal');
                    loadMembers();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Network error occurred');
            });
        });

        // Handle credit adjustment form submission
        document.getElementById('creditForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            formData.append('action', 'adjust_credit');
            
            fetch('admin_member_management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Credit adjusted successfully!');
                    closeModal('creditModal');
                    loadMembers();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Network error occurred');
            });
        });

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>
